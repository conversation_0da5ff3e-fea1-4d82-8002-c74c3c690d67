import { ref } from "vue";
import * as echarts from "echarts";

export const useLine = (setOption: (options: echarts.EChartsOption, opts?: echarts.SetOptionOpts) => void) => {
  // 点位数据
  const points = ref<Array<{ name: string; value: any }>>([]);
  // 连线数据
  const lines = ref<Array<{ coords: any }>>([]);
  // 添加飞线回调
  const handleAddLine = (mapName: string) => {
    const { geoJSON } = echarts.getMap(mapName);
    // 取出当前地图所有的非空子级
    const cities = geoJSON.features
      .filter((f: Record<string, any>) => f.properties.name)
      .map((m: Record<string, any>) => {
        return {
          name: m.properties.name,
          value: m.properties.center,
        };
      });

    // 一次性添加多条飞线，增强科技感
    const lineCount = Math.floor(Math.random() * 3) + 2; // 2-4条线

    for (let i = 0; i < lineCount; i++) {
      // 防止两个点重复
      let startIndex = 0;
      let endIndex = 0;
      while (startIndex === endIndex) {
        startIndex = Math.floor(Math.random() * cities.length);
        endIndex = Math.floor(Math.random() * cities.length);
      }
      const startAddress = cities[startIndex];
      const endAddress = cities[endIndex];

      // 起始点
      if (!points.value.find((f) => f.name === startAddress.name)) {
        points.value.push({
          name: startAddress.name,
          value: [...startAddress.value, Math.random() * 10 + 5], // 添加高度值
        });
      }
      // 结束点
      if (!points.value.find((f) => f.name === endAddress.name)) {
        points.value.push({
          name: endAddress.name,
          value: [...endAddress.value, Math.random() * 10 + 5], // 添加高度值
        });
      }
      // 连接线，添加高度信息
      const startCoord = [...cities[startIndex].value, Math.random() * 15 + 10];
      const endCoord = [...cities[endIndex].value, Math.random() * 15 + 10];
      lines.value.push({ coords: [startCoord, endCoord] });
    }

    setOption({
      series: [
        { data: points.value },
        { data: lines.value },
        { data: [] }, // bar3D数据
      ],
    });
  };
  // 清空飞线数据
  const resetLineData = () => {
    points.value = [];
    lines.value = [];
  };

  return {
    points,
    lines,
    handleAddLine,
    resetLineData,
  };
};

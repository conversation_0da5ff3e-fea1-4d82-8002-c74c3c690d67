# ECharts 3D地图颜色问题解决方案

## 🔍 问题分析

### 问题现象
地图的 `itemStyle.areaColor` 颜色配置不生效，无论如何修改颜色值，地图区域颜色都不会改变。

### 根本原因
**ECharts GL 中的 `shading` 属性设置为 `"realistic"` 时，会使用基于物理的渲染（PBR），这会覆盖 `itemStyle.areaColor` 的颜色设置。**

## 📚 技术原理

### ECharts GL 支持的三种着色方式：

1. **`'color'`** - 只显示颜色，不受光照等其它因素的影响
2. **`'lambert'`** - 通过经典的 lambert 着色表现光照带来的明暗
3. **`'realistic'`** - 真实感渲染，使用基于物理的渲染（PBR）

### 关键点：
- 当 `shading: "realistic"` 时，地图使用物理渲染材质，`itemStyle.areaColor` 会被忽略
- 当 `shading: "color"` 时，地图直接使用 `itemStyle.areaColor` 设置的颜色
- 当 `shading: "lambert"` 时，会在 `itemStyle.areaColor` 基础上应用光照效果

## ✅ 解决方案

### 方案一：使用 color 模式（推荐）
```javascript
geo3D: {
  shading: "color", // 使用color模式，itemStyle.areaColor会直接生效
  itemStyle: {
    areaColor: {
      type: "linear",
      colorStops: [
        { offset: 0, color: "rgba(25, 55, 110, 0.85)" },
        { offset: 1, color: "rgba(55, 95, 170, 0.85)" }
      ]
    }
  }
}
```

### 方案二：使用 lambert 模式
```javascript
geo3D: {
  shading: "lambert", // 保留光照效果，同时支持自定义颜色
  itemStyle: {
    areaColor: "your-color"
  },
  light: {
    main: {
      intensity: 1.2, // 调整光照强度
      color: "#ffffff"
    }
  }
}
```

### 方案三：realistic 模式下使用材质颜色
```javascript
geo3D: {
  shading: "realistic",
  realisticMaterial: {
    baseColorTexture: null, // 不使用纹理
    baseColorFactor: [0.1, 0.2, 0.4, 1.0], // RGBA格式的基础颜色
    roughness: 0.5,
    metalness: 0.0
  }
}
```

## 🎯 当前项目的修复

我们将 `shading` 从 `"realistic"` 改为 `"color"`：

```javascript
// 修改前
shading: "realistic",

// 修改后  
shading: "color", // 使用color模式，这样itemStyle.areaColor才会生效
```

## 🌟 效果对比

### 修改前：
- 使用 `realistic` 渲染
- `itemStyle.areaColor` 被忽略
- 地图显示默认的物理材质颜色

### 修改后：
- 使用 `color` 渲染
- `itemStyle.areaColor` 完全生效
- 地图显示我们设置的深蓝色渐变

## 📖 参考资料

1. [ECharts GL 官方文档 - geo3D.shading](https://echarts.apache.org/zh/option-gl.html#geo3D.shading)
2. [ECharts GL 着色效果说明](https://echarts.apache.org/zh/option-gl.html#geo3D.realisticMaterial)
3. [基于物理的渲染（PBR）原理](https://learnopengl.com/PBR/Theory)

## 💡 最佳实践

1. **简单颜色需求**：使用 `shading: "color"`
2. **需要光照效果**：使用 `shading: "lambert"`  
3. **追求真实感**：使用 `shading: "realistic"` + `realisticMaterial` 配置
4. **性能考虑**：`color` > `lambert` > `realistic`

现在地图的颜色配置应该可以正常生效了！🎉

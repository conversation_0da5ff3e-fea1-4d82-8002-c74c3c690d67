<template>
  <div class="container">
    <div class="map-info">
      <p>当前地图: {{ currentMap.name }}</p>
      <p>当前代码: {{ currentMap.adcode }}</p>
      <p v-if="currentMap.navList.length > 1" class="info-nav">
        <span>层级导航: </span>
        <span v-for="(nav, index) in currentMap.navList" :key="nav.adcode" class="nav-item">
          <span class="item-title" @click="handleLevelChange(nav, index)">{{ nav.name }}</span>
          <span class="item-arrow">-></span>
        </span>
      </p>
      <p>当前飞线: {{ lines.length }}</p>
      <button class="line-btn" @click="handleAddLine(currentMap.name)">随机添加飞线</button>
    </div>
    <div ref="chartRef" class="map-chart"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import "echarts-gl";

import { useECharts } from "./hooks/useEcharts";
import { useMap } from "./hooks/useMap";
import { useLine } from "./hooks/useLine";

const chartRef = ref();
const { setOption, getInstance } = useECharts(chartRef, true);
const { points, lines, handleAddLine, resetLineData } = useLine(setOption);
const { currentMap, registerMap, handleMapClick, handleLevelChange } = useMap(setOption, resetLineData);

// 图表静态配置
const chartOptions: any = {
  backgroundColor: "#0a1a3a", // 深蓝色背景，类似图片中的深海蓝
  geo3D: {
    map: currentMap.name,
    roam: true,
    shading: "realistic",
    regionHeight: 12, // 增加区域高度，增强3D效果
    boxDepth: 120, // 增加盒子深度
    // 添加地图边界线配置
    groundPlane: {
      show: false, // 不显示地面
    },
    viewControl: {
      distance: 120,
      alpha: 35,
      beta: 0,
      panSensitivity: 1,
      zoomSensitivity: 1,
      rotateSensitivity: 1,
      autoRotate: false,
    },
    label: {
      show: true,
      color: "#ffffff",
      fontSize: 14, // 增大字体
      fontFamily: "微软雅黑",
      fontWeight: "bold", // 加粗字体
      backgroundColor: "rgba(0, 0, 0, 0.8)", // 增强背景透明度
      borderColor: "#00ffff", // 更亮的边框颜色
      borderWidth: 2, // 增加边框宽度
      padding: [6, 12], // 增加内边距
      borderRadius: 6, // 增加圆角
      shadowColor: "rgba(0, 255, 255, 0.8)", // 添加阴影效果
      shadowBlur: 10,
      shadowOffsetX: 0,
      shadowOffsetY: 2,
      // 添加文字描边效果，增强可读性
      textShadowColor: "rgba(0, 0, 0, 0.8)",
      textShadowBlur: 3,
      textShadowOffsetX: 1,
      textShadowOffsetY: 1,
    },
    itemStyle: {
      // 增强边界线效果
      borderColor: "rgba(135, 206, 250, 0.6)", // 浅蓝色边界，模仿图片效果
      borderWidth: 1, // 细边界线
      areaColor: {
        // 深蓝色渐变 - 模仿图片中的主要区域颜色
        type: "linear",
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "rgba(25, 55, 110, 0.85)", // 深蓝色起始
          },
          {
            offset: 0.5,
            color: "rgba(40, 75, 140, 0.8)", // 中等蓝色
          },
          {
            offset: 1,
            color: "rgba(55, 95, 170, 0.85)", // 较亮蓝色结束
          },
        ],
      },
      opacity: 0.9,
      shadowColor: "rgba(25, 55, 110, 0.5)", // 深蓝色阴影
      shadowBlur: 60,
      shadowOffsetX: 0,
      shadowOffsetY: 0,
    },
    emphasis: {
      label: {
        color: "#fff",
        fontSize: 16,
        backgroundColor: "rgba(0, 0, 0, 0.9)",
        borderColor: "#00ffff",
        borderWidth: 2,
      },
      itemStyle: {
        // 鼠标悬停时的高亮效果
        areaColor: {
          // 悬停时的青绿色高亮 - 模仿图片中的高亮区域
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(0, 200, 150, 0.9)", // 青绿色起始
            },
            {
              offset: 0.5,
              color: "rgba(20, 220, 170, 0.8)", // 亮青绿色
            },
            {
              offset: 1,
              color: "rgba(40, 240, 190, 0.9)", // 明亮青绿色
            },
          ],
        },
        borderWidth: 2, // 悬停时边界
        borderColor: "rgba(255, 255, 255, 0.9)", // 悬停时白色边界
        shadowColor: "rgba(0, 200, 150, 0.8)",
        shadowBlur: 60,
      },
    },
    // 真实感材质相关配置
    realisticMaterial: {
      detailTexture: "#fff",
      textureTiling: 1,
      roughness: 0.05,
      metalness: 0.3,
    },
    // 光照配置
    light: {
      main: {
        intensity: 1.8,
        shadow: true,
        shadowQuality: "high",
        alpha: 25,
        beta: 35,
        color: "#1cccff",
      },
      ambient: {
        intensity: 0.6,
        color: "#3FBCCE",
      },
      ambientCubemap: {
        texture: null,
        exposure: 1.2,
        diffuseIntensity: 0.7,
        specularIntensity: 2.5,
      },
    },
    // 后处理效果
    postEffect: {
      enable: true,
      bloom: {
        enable: true,
        intensity: 0.5,
        bloomRadius: 0.8,
      },
      SSAO: {
        enable: true,
        quality: "high",
        radius: 3,
        intensity: 1.2,
      },
      screenSpaceReflection: {
        enable: true,
        quality: "medium",
        maxRoughness: 0.8,
      },
    },
  },
  series: [
    // 参考https://echarts.apache.org/zh/option.html#series-scatter3D
    {
      type: "scatter3D",
      coordinateSystem: "geo3D",
      symbolSize: 15,
      symbol: "circle",
      itemStyle: {
        color: {
          type: "radial",
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            {
              offset: 0,
              color: "#fff",
            },
            {
              offset: 0.7,
              color: "#1cccff",
            },
            {
              offset: 1,
              color: "#3FBCCE",
            },
          ],
        },
        opacity: 0.95,
        borderColor: "#FFFFCC",
        borderWidth: 2,
      },
      emphasis: {
        itemStyle: {
          color: "#ffffff",
          borderColor: "#1cccff",
          borderWidth: 3,
        },
      },
      data: points.value,
    },
    // 参考https://echarts.apache.org/zh/option.html#series-lines3D
    {
      type: "lines3D",
      coordinateSystem: "geo3D",
      effect: {
        show: true,
        period: 3,
        trailLength: 0.4,
        trailWidth: 8,
        trailOpacity: 1,
        trailColor: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 255, 255, 0)",
            },
            {
              offset: 0.3,
              color: "rgba(255, 255, 255, 0.8)",
            },
            {
              offset: 0.7,
              color: "rgba(28, 204, 255, 1)",
            },
            {
              offset: 1,
              color: "rgba(63, 188, 206, 0)",
            },
          ],
        },
        constantSpeed: 40,
        symbol: "arrow",
        symbolSize: [12, 8],
      },
      lineStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 255, 255, 0.9)",
            },
            {
              offset: 0.5,
              color: "rgba(28, 204, 255, 0.95)",
            },
            {
              offset: 1,
              color: "rgba(63, 188, 206, 0.9)",
            },
          ],
        },
        width: 1,
        opacity: 0.1,
      },
      data: lines.value,
    },
    // 添加发光柱状图3D效果
    {
      type: "bar3D",
      coordinateSystem: "geo3D",
      barSize: 0.8,
      minHeight: 3,
      silent: true,
      itemStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 215, 0, 0.9)",
            },
            {
              offset: 1,
              color: "rgba(255, 140, 0, 0.6)",
            },
          ],
        },
        opacity: 0.7,
        borderColor: "#ffff00",
        borderWidth: 1,
      },
      data: [],
    },
  ],
};

// 页面加载完成
onMounted(async () => {
  // 注册地图
  await registerMap();
  // 绑定点击事件
  const instance = getInstance();
  instance?.on("click", handleMapClick);
  setOption(chartOptions);
});
</script>

<style lang="less">
* {
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  line-height: 1;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: #0a1a3a; // 深蓝色背景，与地图背景一致
}
#app {
  width: 100%;
  height: 100%;
  color: #ffffff;
  font-size: 12px;
  font-family: "'Pingfang SC', 'SF UI Text', 'Helvetica Neue', 'Consolas'";
  overflow: hidden;
}
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  .map-info {
    height: auto;
    max-height: 300px;
    color: #ffffff;
    font-weight: 600;
    font-size: 16px;
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 9;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #1cccff;
    box-shadow:
      0 0 30px rgba(28, 204, 255, 0.4),
      inset 0 0 20px rgba(28, 204, 255, 0.1);
    backdrop-filter: blur(10px);
    .nav-item {
      .item-title {
        cursor: pointer;
        color: #ffffff;
        text-decoration: none;
        padding: 4px 8px;
        border-radius: 6px;
        transition: all 0.3s ease;
        &:hover {
          background: rgba(28, 204, 255, 0.2);
          color: #fff;
          box-shadow: 0 0 10px rgba(28, 204, 255, 0.3);
        }
      }
      .item-arrow {
        color: #3fbcce;
        margin: 0 8px;
      }
      &:last-child {
        .item-arrow {
          display: none;
        }
      }
    }
    .line-btn {
      outline: none;
      cursor: pointer;
      background: linear-gradient(135deg, #1cccff, #3fbcce);
      color: #000000;
      border: 1px solid #1cccff;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: bold;
      font-size: 14px;
      margin-top: 15px;
      transition: all 0.3s ease;
      box-shadow:
        0 0 20px rgba(28, 204, 255, 0.4),
        inset 0 0 10px rgba(255, 255, 255, 0.2);
      &:hover {
        background: linear-gradient(135deg, #fff, #1cccff);
        box-shadow:
          0 0 30px rgba(28, 204, 255, 0.6),
          inset 0 0 15px rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        border-color: #fff;
      }
      &:active {
        transform: translateY(-1px);
      }
    }
  }
  .map-chart {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>

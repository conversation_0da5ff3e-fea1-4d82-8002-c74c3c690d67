# 地图颜色问题排查记录

## 🔍 问题现象
地图显示为白色/透明，而不是设置的深蓝色。

## 🛠️ 已排查的问题点

### 1. ✅ shading 模式问题
- **问题**：`shading: "realistic"` 会忽略 `itemStyle.areaColor`
- **解决**：改为 `shading: "color"`

### 2. ✅ useMap.ts 中的颜色覆盖
- **问题**：`getRandomAreaColor()` 函数在地图切换时覆盖主配置
- **解决**：将随机颜色改为固定颜色

### 3. ✅ 后处理效果干扰
- **问题**：`postEffect` 可能影响颜色渲染
- **解决**：暂时禁用后处理效果

### 4. ✅ 光照配置干扰
- **问题**：在 `color` 模式下，光照配置可能仍然影响显示
- **解决**：注释掉光照配置

### 5. ✅ 真实感材质配置
- **问题**：`realisticMaterial` 在 `color` 模式下不需要
- **解决**：注释掉材质配置

## 🧪 当前测试配置

```javascript
geo3D: {
  map: currentMap.name,
  roam: true,
  shading: "color", // 使用color模式
  regionHeight: 12,
  boxDepth: 120,
  groundPlane: {
    show: false,
  },
  viewControl: {
    distance: 120,
    alpha: 35,
    beta: 0,
    // ...其他视角控制
  },
  itemStyle: {
    areaColor: "#ff0000", // 红色测试
    borderColor: "#ffffff",
    borderWidth: 1,
  },
  // 禁用了光照、材质、后处理效果
}
```

## 🔍 待排查的可能原因

### 1. 浏览器兼容性
- WebGL 支持问题
- ECharts GL 版本兼容性

### 2. 地图数据问题
- GeoJSON 数据格式
- 地图注册问题

### 3. CSS 样式干扰
- z-index 层级问题
- 容器样式影响

### 4. ECharts 实例问题
- 初始化时机
- 配置更新方式

## 🎯 下一步排查方向

1. **确认红色测试结果**
   - 如果红色显示正常 → 改回深蓝色
   - 如果仍然白色 → 继续排查

2. **检查浏览器控制台**
   - WebGL 错误
   - ECharts 警告信息

3. **简化配置测试**
   - 最小化配置
   - 逐步添加功能

4. **版本兼容性检查**
   - ECharts 版本
   - ECharts GL 版本
   - 浏览器版本

## 📝 测试记录

- [ ] 红色测试是否显示正常
- [ ] 浏览器控制台是否有错误
- [ ] 不同浏览器测试结果
- [ ] 简化配置测试结果

## 💡 可能的解决方案

1. **如果红色正常显示**：
   ```javascript
   areaColor: "#1e3c72" // 改回深蓝色
   ```

2. **如果仍然白色**：
   - 检查 WebGL 支持
   - 降级到 2D 地图
   - 更新 ECharts GL 版本

3. **备用方案**：
   ```javascript
   // 使用 lambert 模式
   shading: "lambert",
   light: {
     main: { intensity: 0.8 },
     ambient: { intensity: 0.3 }
   }
   ```

<template>
  <div class="container">
    <div class="map-info">
      <p>当前地图: {{ currentMap.name }}</p>
      <p>当前代码: {{ currentMap.adcode }}</p>
      <p v-if="currentMap.navList.length > 1" class="info-nav">
        <span>层级导航: </span>
        <span v-for="(nav, index) in currentMap.navList" :key="nav.adcode" class="nav-item">
          <span class="item-title" @click="handleLevelChange(nav, index)">{{ nav.name }}</span>
          <span class="item-arrow">-></span>
        </span>
      </p>
      <p>当前飞线: {{ lines.length }}</p>
      <button class="line-btn" @click="handleAddLine(currentMap.name)">随机添加飞线</button>
    </div>
    <div ref="chartRef" class="map-chart"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import "echarts-gl";

import { useECharts } from "./hooks/useEcharts";
import { useMap } from "./hooks/useMap";
import { useLine } from "./hooks/useLine";

const chartRef = ref();
const { setOption, getInstance } = useECharts(chartRef, true);
const { points, lines, handleAddLine, resetLineData } = useLine(setOption);
const { currentMap, registerMap, handleMapClick, handleLevelChange } = useMap(setOption, resetLineData);

// 图表静态配置
const chartOptions: any = {
  backgroundColor: "#000f1e",
  // 参考https://echarts.apache.org/zh/option.html#geo3D
  geo3D: {
    map: currentMap.name,
    roam: true,
    shading: "realistic",
    regionHeight: 8,
    boxDepth: 100,
    viewControl: {
      distance: 120,
      alpha: 35,
      beta: 0,
      panSensitivity: 1,
      zoomSensitivity: 1,
      rotateSensitivity: 1,
      autoRotate: false,
    },
    label: {
      show: true,
      color: "#b3e2f2",
      fontSize: 12,
      fontFamily: "微软雅黑",
      backgroundColor: "rgba(166, 200, 76, 0.82)",
      borderColor: "#FFFFCC",
      borderWidth: 1,
      padding: [4, 8],
      borderRadius: 4,
    },
    itemStyle: {
      borderColor: "#1cccff",
      borderWidth: 1,
      areaColor: "rgba(5,135,177,0.6)",
      opacity: 0.5,
      shadowColor: "rgba(56,192,255,0.3)",
      shadowBlur: 50,
    },
    emphasis: {
      label: {
        color: "#fff",
        fontSize: 16,
        backgroundColor: "rgba(166, 200, 76, 0.82)",
        borderColor: "#FFFFCC",
      },
      itemStyle: {
        areaColor: "rgba(5,216,252,0.5)",
        borderWidth: 1,
        borderColor: "#3FBCCE",
      },
    },
    // 真实感材质相关配置
    realisticMaterial: {
      detailTexture: "#fff",
      textureTiling: 1,
      roughness: 0.05,
      metalness: 0.3,
    },
    // 光照配置
    light: {
      main: {
        intensity: 1.8,
        shadow: true,
        shadowQuality: "high",
        alpha: 25,
        beta: 35,
        color: "#1cccff",
      },
      ambient: {
        intensity: 0.6,
        color: "#3FBCCE",
      },
      ambientCubemap: {
        texture: null,
        exposure: 1.2,
        diffuseIntensity: 0.7,
        specularIntensity: 2.5,
      },
    },
    // 后处理效果
    postEffect: {
      enable: true,
      bloom: {
        enable: true,
        intensity: 0.5,
        bloomRadius: 0.8,
      },
      SSAO: {
        enable: true,
        quality: "high",
        radius: 3,
        intensity: 1.2,
      },
      screenSpaceReflection: {
        enable: true,
        quality: "medium",
        maxRoughness: 0.8,
      },
    },
  },
  series: [
    // 参考https://echarts.apache.org/zh/option.html#series-scatter3D
    {
      type: "scatter3D",
      coordinateSystem: "geo3D",
      symbolSize: 15,
      symbol: "circle",
      itemStyle: {
        color: {
          type: "radial",
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            {
              offset: 0,
              color: "#fff",
            },
            {
              offset: 0.7,
              color: "#1cccff",
            },
            {
              offset: 1,
              color: "#3FBCCE",
            },
          ],
        },
        opacity: 0.95,
        borderColor: "#FFFFCC",
        borderWidth: 2,
      },
      emphasis: {
        itemStyle: {
          color: "#ffffff",
          borderColor: "#1cccff",
          borderWidth: 3,
        },
      },
      data: points.value,
    },
    // 参考https://echarts.apache.org/zh/option.html#series-lines3D
    {
      type: "lines3D",
      coordinateSystem: "geo3D",
      effect: {
        show: true,
        period: 3,
        trailLength: 0.4,
        trailWidth: 8,
        trailOpacity: 1,
        trailColor: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 255, 255, 0)",
            },
            {
              offset: 0.3,
              color: "rgba(255, 255, 255, 0.8)",
            },
            {
              offset: 0.7,
              color: "rgba(28, 204, 255, 1)",
            },
            {
              offset: 1,
              color: "rgba(63, 188, 206, 0)",
            },
          ],
        },
        constantSpeed: 40,
        symbol: "arrow",
        symbolSize: [12, 8],
      },
      lineStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 255, 255, 0.9)",
            },
            {
              offset: 0.5,
              color: "rgba(28, 204, 255, 0.95)",
            },
            {
              offset: 1,
              color: "rgba(63, 188, 206, 0.9)",
            },
          ],
        },
        width: 1,
        opacity: 0.1,
      },
      data: lines.value,
    },
    // 添加发光柱状图3D效果
    {
      type: "bar3D",
      coordinateSystem: "geo3D",
      barSize: 0.8,
      minHeight: 3,
      silent: true,
      itemStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 215, 0, 0.9)",
            },
            {
              offset: 1,
              color: "rgba(255, 140, 0, 0.6)",
            },
          ],
        },
        opacity: 0.7,
        borderColor: "#ffff00",
        borderWidth: 1,
      },
      data: [],
    },
  ],
};

// 页面加载完成
onMounted(async () => {
  // 注册地图
  await registerMap();
  // 绑定点击事件
  const instance = getInstance();
  instance?.on("click", handleMapClick);
  setOption(chartOptions);
});
</script>

<style lang="less">
* {
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  line-height: 1;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: #000f1e;
}
#app {
  width: 100%;
  height: 100%;
  color: #b3e2f2;
  font-size: 12px;
  font-family: "'Pingfang SC', 'SF UI Text', 'Helvetica Neue', 'Consolas'";
  overflow: hidden;
}
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  .map-info {
    height: auto;
    max-height: 300px;
    color: #b3e2f2;
    font-weight: 600;
    font-size: 16px;
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 9;
    background: rgba(166, 200, 76, 0.82);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #ffffcc;
    box-shadow:
      0 0 30px rgba(28, 204, 255, 0.4),
      inset 0 0 20px rgba(28, 204, 255, 0.1);
    backdrop-filter: blur(10px);
    .nav-item {
      .item-title {
        cursor: pointer;
        color: #b3e2f2;
        text-decoration: none;
        padding: 4px 8px;
        border-radius: 6px;
        transition: all 0.3s ease;
        &:hover {
          background: rgba(28, 204, 255, 0.2);
          color: #fff;
          box-shadow: 0 0 10px rgba(28, 204, 255, 0.3);
        }
      }
      .item-arrow {
        color: #3fbcce;
        margin: 0 8px;
      }
      &:last-child {
        .item-arrow {
          display: none;
        }
      }
    }
    .line-btn {
      outline: none;
      cursor: pointer;
      background: linear-gradient(135deg, #1cccff, #3fbcce);
      color: #000f1e;
      border: 1px solid #1cccff;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: bold;
      font-size: 14px;
      margin-top: 15px;
      transition: all 0.3s ease;
      box-shadow:
        0 0 20px rgba(28, 204, 255, 0.4),
        inset 0 0 10px rgba(255, 255, 255, 0.2);
      &:hover {
        background: linear-gradient(135deg, #fff, #1cccff);
        box-shadow:
          0 0 30px rgba(28, 204, 255, 0.6),
          inset 0 0 15px rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        border-color: #fff;
      }
      &:active {
        transform: translateY(-1px);
      }
    }
  }
  .map-chart {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>

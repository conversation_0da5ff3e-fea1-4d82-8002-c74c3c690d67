import { reactive } from "vue";
import * as echarts from "echarts";
import { get<PERSON><PERSON><PERSON><PERSON> } from "@/apis/index";

export const useMap = (
  setOption: (options: echarts.EChartsOption, opts?: echarts.SetOptionOpts) => void,
  resetLineData: () => void,
) => {
  // 当前地图信息
  const currentMap = reactive({
    name: "浙江省",
    adcode: 330000,
    navList: [{ name: "浙江省", adcode: 330000, level: "province" }],
  });
  // 地区code映射
  const adcodeMap: Record<string, { adcode: number; level: string }> = {};

  // 预定义的颜色主题数组，用于区分不同区域
  const colorThemes = [
    {
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(5,135,177,0.7)" },
        { offset: 0.5, color: "rgba(28,204,255,0.6)" },
        { offset: 1, color: "rgba(63,188,206,0.8)" }
      ]
    },
    {
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(138,43,226,0.7)" },
        { offset: 0.5, color: "rgba(75,0,130,0.6)" },
        { offset: 1, color: "rgba(148,0,211,0.8)" }
      ]
    },
    {
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(255,69,0,0.7)" },
        { offset: 0.5, color: "rgba(255,140,0,0.6)" },
        { offset: 1, color: "rgba(255,165,0,0.8)" }
      ]
    },
    {
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(34,139,34,0.7)" },
        { offset: 0.5, color: "rgba(0,255,127,0.6)" },
        { offset: 1, color: "rgba(50,205,50,0.8)" }
      ]
    }
  ];

  // 获取随机区域颜色
  const getRandomAreaColor = () => {
    return colorThemes[Math.floor(Math.random() * colorThemes.length)];
  };

  // 生成地图code映射
  const genAdcodeMap = (features: any[]) => {
    features.forEach((feature) => {
      const { name, adcode, level } = feature.properties;
      adcodeMap[name] = { adcode, level };
    });
  };
  // 注册地图
  const registerMap = async (mapInfo?: { name: string; adcode: number }) => {
    const _mapInfo = mapInfo ?? currentMap;
    if (echarts.getMap(_mapInfo.name)) return;
    await getGeojson(_mapInfo.adcode).then((geoJSON: any) => {
      genAdcodeMap(geoJSON.features);
      echarts.registerMap(_mapInfo.name, geoJSON);
    });
  };
  // 获取地图信息
  const getMapInfo = (name?: string) => {
    const _name = name ?? currentMap.name;
    return echarts.getMap(_name);
  };
  // 地图点击回调
  const handleMapClick = (params: any) => {
    const { adcode, level } = adcodeMap[params.name];
    if (level === "district") return;
    resetLineData();
    registerMap({ name: params.name, adcode }).then(() => {
      currentMap.name = params.name;
      currentMap.adcode = adcode;
      currentMap.navList.push({ name: params.name, adcode, level });
      setOption({
        geo3D: {
          map: params.name,
          // 为新地图设置不同的颜色主题
          itemStyle: {
            borderColor: "#00ffff",
            borderWidth: 2,
            areaColor: getRandomAreaColor(),
          }
        },
        series: [{ data: [] }, { data: [] }, { data: [] }]
      });
    });
  };
  // 地图层级切换回调
  const handleLevelChange = (params: (typeof currentMap.navList)[number], index: number) => {
    currentMap.navList.splice(index + 1, 9);
    currentMap.name = params.name;
    currentMap.adcode = params.adcode;
    resetLineData();
    setOption({
      geo3D: {
        map: params.name,
        zoom: 1,
        center: undefined,
        // 为切换的地图设置新的颜色主题
        itemStyle: {
          borderColor: "#00ffff",
          borderWidth: 2,
          areaColor: getRandomAreaColor(),
        }
      },
      series: [{ data: [] }, { data: [] }, { data: [] }]
    });
  };

  return {
    currentMap,
    adcodeMap,
    registerMap,
    getMapInfo,
    handleMapClick,
    handleLevelChange,
  };
};

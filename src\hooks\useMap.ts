import { reactive } from "vue";
import * as echarts from "echarts";
import { getG<PERSON><PERSON><PERSON> } from "@/apis/index";

export const useMap = (
  setOption: (options: echarts.EChartsOption, opts?: echarts.SetOptionOpts) => void,
  resetLineData: () => void,
) => {
  // 当前地图信息
  const currentMap = reactive({
    name: "浙江省",
    adcode: 330000,
    navList: [{ name: "浙江省", adcode: 330000, level: "province" }],
  });
  // 地区code映射
  const adcodeMap: Record<string, { adcode: number; level: string }> = {};

  // 预定义的颜色主题数组，用于区分不同区域 - 深蓝色系配色方案
  const colorThemes = [
    {
      // 主要深蓝色主题
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(25, 55, 110, 0.85)" },
        { offset: 0.5, color: "rgba(40, 75, 140, 0.8)" },
        { offset: 1, color: "rgba(55, 95, 170, 0.85)" }
      ]
    },
    {
      // 稍深的蓝色主题
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(20, 45, 95, 0.85)" },
        { offset: 0.5, color: "rgba(35, 65, 125, 0.8)" },
        { offset: 1, color: "rgba(50, 85, 155, 0.85)" }
      ]
    },
    {
      // 中等蓝色主题
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(30, 65, 125, 0.85)" },
        { offset: 0.5, color: "rgba(45, 85, 155, 0.8)" },
        { offset: 1, color: "rgba(60, 105, 185, 0.85)" }
      ]
    },
    {
      // 较亮的蓝色主题
      type: "linear",
      x: 0, y: 0, x2: 1, y2: 1,
      colorStops: [
        { offset: 0, color: "rgba(35, 70, 135, 0.85)" },
        { offset: 0.5, color: "rgba(50, 90, 165, 0.8)" },
        { offset: 1, color: "rgba(65, 110, 195, 0.85)" }
      ]
    }
  ];

  // 获取随机区域颜色
  const getRandomAreaColor = () => {
    return colorThemes[Math.floor(Math.random() * colorThemes.length)];
  };

  // 生成地图code映射
  const genAdcodeMap = (features: any[]) => {
    features.forEach((feature) => {
      const { name, adcode, level } = feature.properties;
      adcodeMap[name] = { adcode, level };
    });
  };
  // 注册地图
  const registerMap = async (mapInfo?: { name: string; adcode: number }) => {
    const _mapInfo = mapInfo ?? currentMap;
    if (echarts.getMap(_mapInfo.name)) return;
    await getGeojson(_mapInfo.adcode).then((geoJSON: any) => {
      genAdcodeMap(geoJSON.features);
      echarts.registerMap(_mapInfo.name, geoJSON);
    });
  };
  // 获取地图信息
  const getMapInfo = (name?: string) => {
    const _name = name ?? currentMap.name;
    return echarts.getMap(_name);
  };
  // 地图点击回调
  const handleMapClick = (params: any) => {
    const { adcode, level } = adcodeMap[params.name];
    if (level === "district") return;
    resetLineData();
    registerMap({ name: params.name, adcode }).then(() => {
      currentMap.name = params.name;
      currentMap.adcode = adcode;
      currentMap.navList.push({ name: params.name, adcode, level });
      setOption({
        geo3D: {
          map: params.name,
          // 为新地图设置深蓝色主题
          itemStyle: {
            borderColor: "rgba(135, 206, 250, 0.6)",
            borderWidth: 1,
            areaColor: "#2a5aa0", // 深蓝色
          }
        },
        series: [{ data: [] }, { data: [] }, { data: [] }]
      });
    });
  };
  // 地图层级切换回调
  const handleLevelChange = (params: (typeof currentMap.navList)[number], index: number) => {
    currentMap.navList.splice(index + 1, 9);
    currentMap.name = params.name;
    currentMap.adcode = params.adcode;
    resetLineData();
    setOption({
      geo3D: {
        map: params.name,
        zoom: 1,
        center: undefined,
        // 为切换的地图设置深蓝色主题
        itemStyle: {
          borderColor: "rgba(135, 206, 250, 0.6)",
          borderWidth: 1,
          areaColor: "#2a5aa0", // 深蓝色
        }
      },
      series: [{ data: [] }, { data: [] }, { data: [] }]
    });
  };

  return {
    currentMap,
    adcodeMap,
    registerMap,
    getMapInfo,
    handleMapClick,
    handleLevelChange,
  };
};

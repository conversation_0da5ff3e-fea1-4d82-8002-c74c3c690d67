<template>
  <div class="container">
    <div class="map-info">
      <p>当前地图: {{ currentMap.name }}</p>
      <p>当前代码: {{ currentMap.adcode }}</p>
      <p v-if="currentMap.navList.length > 1" class="info-nav">
        <span>层级导航: </span>
        <span v-for="(nav, index) in currentMap.navList" :key="nav.adcode" class="nav-item">
          <span class="item-title" @click="handleLevelChange(nav, index)">{{ nav.name }}</span>
          <span class="item-arrow">-></span>
        </span>
      </p>
      <p>当前飞线: {{ lines.length }}</p>
      <button class="line-btn" @click="handleAddLine(currentMap.name)">随机添加飞线</button>
    </div>
    <div ref="chartRef" class="map-chart"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import "echarts-gl";

import { useECharts } from "./hooks/useEcharts";
import { useMap } from "./hooks/useMap";
import { useLine } from "./hooks/useLine";

const chartRef = ref();
const { setOption, getInstance } = useECharts(chartRef, true);
const { points, lines, handleAddLine, resetLineData } = useLine(setOption);
const { currentMap, registerMap, handleMapClick, handleLevelChange } = useMap(setOption, resetLineData);

// 图表静态配置
const chartOptions: any = {
  backgroundColor: {
    type: "radial",
    x: 0.5,
    y: 0.5,
    r: 0.8,
    colorStops: [
      {
        offset: 0,
        color: "#1a2332",
      },
      {
        offset: 0.7,
        color: "#0f1419",
      },
      {
        offset: 1,
        color: "#000000",
      },
    ],
  },
  // 参考https://echarts.apache.org/zh/option.html#geo3D
  geo3D: {
    map: currentMap.name,
    roam: true,
    shading: "realistic",
    regionHeight: 8,
    boxDepth: 100,
    viewControl: {
      distance: 120,
      alpha: 35,
      beta: 0,
      panSensitivity: 1,
      zoomSensitivity: 1,
      rotateSensitivity: 1,
      autoRotate: false,
    },
    label: {
      show: true,
      color: "#00ffff",
      fontSize: 14,
      fontFamily: "微软雅黑",
      backgroundColor: "rgba(0,0,0,0.3)",
      borderColor: "#00ffff",
      borderWidth: 1,
      padding: [4, 8],
      borderRadius: 4,
    },
    itemStyle: {
      borderColor: "#4dd0e1",
      borderWidth: 1.2,
      areaColor: {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "rgba(77, 208, 225, 0.9)",
          },
          {
            offset: 0.5,
            color: "rgba(38, 166, 154, 0.8)",
          },
          {
            offset: 1,
            color: "rgba(0, 121, 107, 0.7)",
          },
        ],
      },
      opacity: 0.9,
    },
    emphasis: {
      label: {
        color: "#ffffff",
        fontSize: 16,
        backgroundColor: "rgba(77,208,225,0.3)",
        borderColor: "#ffffff",
      },
      itemStyle: {
        areaColor: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(129, 236, 236, 0.95)",
            },
            {
              offset: 0.5,
              color: "rgba(77, 208, 225, 0.85)",
            },
            {
              offset: 1,
              color: "rgba(38, 166, 154, 0.75)",
            },
          ],
        },
        borderWidth: 2.5,
        borderColor: "#81ecec",
      },
    },
    // 真实感材质相关配置
    realisticMaterial: {
      detailTexture: "#fff",
      textureTiling: 1,
      roughness: 0.05,
      metalness: 0.3,
    },
    // 光照配置
    light: {
      main: {
        intensity: 1.8,
        shadow: true,
        shadowQuality: "high",
        alpha: 25,
        beta: 35,
        color: "#b3e5fc",
      },
      ambient: {
        intensity: 0.6,
        color: "#4dd0e1",
      },
      ambientCubemap: {
        texture: null,
        exposure: 1.2,
        diffuseIntensity: 0.7,
        specularIntensity: 2.5,
      },
    },
    // 后处理效果
    postEffect: {
      enable: true,
      bloom: {
        enable: true,
        intensity: 0.3,
      },
      SSAO: {
        enable: true,
        quality: "medium",
        radius: 2,
        intensity: 1,
      },
    },
  },
  series: [
    // 参考https://echarts.apache.org/zh/option.html#series-scatter3D
    {
      type: "scatter3D",
      coordinateSystem: "geo3D",
      symbolSize: 15,
      symbol: "circle",
      itemStyle: {
        color: {
          type: "radial",
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            {
              offset: 0,
              color: "#00ffff",
            },
            {
              offset: 1,
              color: "#0080ff",
            },
          ],
        },
        opacity: 0.9,
        borderColor: "#ffffff",
        borderWidth: 2,
      },
      emphasis: {
        itemStyle: {
          color: "#ffffff",
          borderColor: "#00ffff",
          borderWidth: 3,
        },
      },
      data: points.value,
    },
    // 参考https://echarts.apache.org/zh/option.html#series-lines3D
    {
      type: "lines3D",
      coordinateSystem: "geo3D",
      effect: {
        show: true,
        period: 3,
        trailLength: 0.4,
        trailWidth: 8,
        trailOpacity: 1,
        trailColor: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(0, 255, 255, 0)",
            },
            {
              offset: 0.5,
              color: "rgba(0, 255, 255, 1)",
            },
            {
              offset: 1,
              color: "rgba(0, 128, 255, 0)",
            },
          ],
        },
        constantSpeed: 40,
        symbol: "arrow",
        symbolSize: [12, 8],
      },
      lineStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "rgba(0, 255, 255, 0.8)",
            },
            {
              offset: 1,
              color: "rgba(0, 128, 255, 0.8)",
            },
          ],
        },
        width: 4,
        opacity: 0.8,
      },
      data: lines.value,
    },
    // 添加发光柱状图3D效果
    {
      type: "bar3D",
      coordinateSystem: "geo3D",
      barSize: 0.8,
      minHeight: 3,
      silent: true,
      itemStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(255, 215, 0, 0.9)",
            },
            {
              offset: 1,
              color: "rgba(255, 140, 0, 0.6)",
            },
          ],
        },
        opacity: 0.7,
        borderColor: "#ffff00",
        borderWidth: 1,
      },
      data: [],
    },
  ],
};

// 页面加载完成
onMounted(async () => {
  // 注册地图
  await registerMap();
  // 绑定点击事件
  const instance = getInstance();
  instance?.on("click", handleMapClick);
  setOption(chartOptions);
});
</script>

<style lang="less">
* {
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  line-height: 1;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
#app {
  width: 100%;
  height: 100%;
  color: #000000;
  font-size: 12px;
  font-family: "'Pingfang SC', 'SF UI Text', 'Helvetica Neue', 'Consolas'";
  overflow: hidden;
}
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  .map-info {
    height: auto;
    max-height: 300px;
    color: #00ffff;
    font-weight: 600;
    font-size: 16px;
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 9;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #00ffff;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    .nav-item {
      .item-title {
        cursor: pointer;
        color: #00ffff;
        text-decoration: none;
        padding: 2px 6px;
        border-radius: 4px;
        transition: all 0.3s ease;
        &:hover {
          background: rgba(0, 255, 255, 0.2);
          color: #ffffff;
        }
      }
      .item-arrow {
        color: #00ffff;
        margin: 0 8px;
      }
      &:last-child {
        .item-arrow {
          display: none;
        }
      }
    }
    .line-btn {
      outline: none;
      cursor: pointer;
      background: linear-gradient(45deg, #00ffff, #0080ff);
      color: #000;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: bold;
      font-size: 14px;
      margin-top: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
      &:hover {
        background: linear-gradient(45deg, #ffffff, #00ffff);
        box-shadow: 0 0 25px rgba(0, 255, 255, 0.6);
        transform: translateY(-2px);
      }
      &:active {
        transform: translateY(0);
      }
    }
  }
  .map-chart {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>
